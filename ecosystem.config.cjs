module.exports = {
  apps: [
    {
      name: 'pet-store',
      script: '.output/server/index.mjs',
      cwd: '/home/<USER>/pet-ecommerce-store-V20-frontend',
      instances: 1,
      exec_mode: 'fork',
      autorestart: true,
      env: {
        NODE_ENV: 'production',
        HOST: '0.0.0.0',
        PORT: '3000',
        // Add required runtime envs for Nuxt GraphQL client and bridge
        GQL_HOST: process.env.GQL_HOST || 'https://api.368366.xyz/graphql',
        APP_HOST: process.env.APP_HOST || 'https://368366.xyz',
        NUXT_IMAGE_DOMAINS: process.env.NUXT_IMAGE_DOMAINS || 'wp.example.com,cdn.example.com',
      },
    },
  ],
};


