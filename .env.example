# Required
GQL_HOST="https://wp.example.com/graphql"   # must end with /graphql
NUXT_IMAGE_DOMAINS="wp.example.com,cdn.example.com"  # hostnames only, comma-separated (no https://)

# Optional (used as the Origin header when calling WordPress)
APP_HOST="http://localhost:3000"

# If you don't have the plugin installed, you can configure the rest of the settings here.
# number of products to be displayed per page
NUXT_PUBLIC_PRODUCTS_PER_PAGE="24"

# Stripe API key
NUXT_PUBLIC_STRIPE_PUBLISHABLE_KEY="pk_test_abcdefghijklmnopqrstuvwxyz"

# Color of input and button elements
PRIMARY_COLOR="#ff0000"
