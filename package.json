{"name": "woonuxt", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev --host", "dev:ssl": "NODE_TLS_REJECT_UNAUTHORIZED=0 nuxt dev --https --ssl-cert localhost.pem --ssl-key localhost-key.pem", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "serve": "nuxi generate && npx serve .output/public -l 8080"}, "dependencies": {"@stripe/stripe-js": "^7.7.0", "@vueform/slider": "^2.1.10", "@vueuse/core": "^13.6.0"}, "devDependencies": {"@nuxt/icon": "^1.15.0", "@nuxt/image": "1.11.0", "@nuxtjs/i18n": "^9.5.2", "@nuxtjs/tailwindcss": "^6.14.0", "@tailwindcss/typography": "^0.5.16", "nuxt": "^4.0.3", "nuxt-graphql-client": "^0.2.46", "prettier": "^3.6.2"}}