# 运行手册（Nuxt 4 SSR + PM2 + Nginx Proxy Manager）

本项目采用 Nuxt 4 SSR，使用 PM2 常驻进程运行，80/443 通过 Docker 中的 Nginx Proxy Manager（NPM）反代到 Node 服务。

- 项目路径：`/home/<USER>/pet-ecommerce-store-V20-frontend`
- Node 服务：`HOST=0.0.0.0`，`PORT=3000`
- 反向代理：NPM 容器（占用 80/81/443），转发到宿主机网桥 `**********:3000`

---

## 1. 环境要求

- Node.js 20（nvm 管理）
- 全局 PM2：`npm i -g pm2`
- Docker 运行 Nginx Proxy Manager（端口 80/81/443）
- 可选：Cloudflare 代理（申请证书时建议临时“仅 DNS”）

---

## 2. 首次部署

```bash
cd /home/<USER>/pet-ecommerce-store-V20-frontend
npm ci
npm run build
# 通过 pm2 生态配置启动（已包含 HOST/PORT）
pm2 start ecosystem.config.cjs
pm2 save

# 设置开机自启（按 pm2 提示执行；以下为示例）
sudo env PATH=$PATH:/home/<USER>/.nvm/versions/node/v20.19.4/bin \
  /home/<USER>/.nvm/versions/node/v20.19.4/lib/node_modules/pm2/bin/pm2 \
  startup systemd -u alan --hp /home/<USER>
```

> 如已执行过 `pm2 startup` 与 `pm2 save`，无需重复。

---

## 3. 日常更新发布

```bash
cd /home/<USER>/pet-ecommerce-store-V20-frontend
git pull
npm ci
npm run build
pm2 restart pet-store
pm2 save
```

---

## 4. 启动 / 重启 / 停止 / 状态

```bash
# 启动（按生态配置）
pm2 start ecosystem.config.cjs

# 重启
pm2 restart pet-store

# 停止并移除
pm2 stop pet-store && pm2 delete pet-store && pm2 save

# 查看进程列表
pm2 list

# 查看日志
pm2 logs pet-store --lines 100
```

---

## 5. 构建相关

```bash
# SSR 构建
npm run build

# 本地开发
npm run dev

# 预览（临时）
npm run preview
```

> 生产环境请使用 `npm run build` + `pm2` 托管，不要用 `dev/preview` 长期对外。

---

## 6. 健康检查与自检

```bash
# 本机端口监听
ss -ltnp | grep :3000

# 本机直连应用
curl -I http://127.0.0.1:3000/

# Docker → 宿主访问（NPM 内部用宿主网桥）
# 若容器缺 curl：docker exec -it npm-data-app-1 sh -lc "apk add --no-cache curl || true"
docker exec -it npm-data-app-1 sh -lc "curl -I http://**********:3000/"

# 外部域名快速验证（HTTP 会 301 到 HTTPS）
curl -I http://368366.xyz
curl -I https://368366.xyz
```

---

## 7. Nginx Proxy Manager（NPM）配置要点

- 后台地址：`http://服务器IP:81`
- 新建 Proxy Host：
  - Domain Names：`368366.xyz`, `www.368366.xyz`
  - Scheme：`http`
  - Forward Hostname/IP：`**********`
  - Forward Port：`3000`
  - 勾选：`Block Common Exploits`、`Websockets Support`
- SSL：
  - 申请 Let's Encrypt 证书（填邮箱，勾选 `Force SSL`、`HTTP/2`、`HSTS`）
  - 如失败，临时将 Cloudflare 记录改为“仅 DNS（灰云）”，成功后可再启用代理（橙云）

---

## 8. 常见问题排障

- 502 网关错误：
  1) 确认 Node 在线：`pm2 list`，日志：`pm2 logs pet-store`
  2) 确认端口监听：`ss -ltnp | grep :3000`
  3) 确认 NPM 转发到 `**********:3000` 且目标在线
  4) Cloudflare 开橙云回源异常 → 先改“仅 DNS”验证，再恢复

- 端口被占用（EADDRINUSE）：
  - 找占用者：`ss -ltnp | grep :3000`
  - 重启进程：`pm2 restart pet-store`
  - 有重复实例：`pm2 list` → `pm2 delete <id|name>`

- 证书申请失败：
  - DNS 未生效/未指向当前服务器
  - Cloudflare 橙云导致 ACME 验证失败 → 临时“仅 DNS”

---

## 9. 备用：静态托管（不走 SSR）

```bash
npm run generate
# 生成到 .output/public，用任意静态服务器/Nginx 托管
```

---

## 10. 速查（Cheat Sheet）

```bash
# 一键发布
cd /home/<USER>/pet-ecommerce-store-V20-frontend && npm ci && npm run build \
  && pm2 restart pet-store && pm2 save

# 服务状态与日志
pm2 list && pm2 logs pet-store --lines 100

# 反代链路（外部）
curl -I https://368366.xyz
```

---

如需调整监听端口或主机名，编辑 `ecosystem.config.cjs` 的 `HOST`/`PORT`：

```bash
pm2 startOrReload ecosystem.config.cjs && pm2 save
```
