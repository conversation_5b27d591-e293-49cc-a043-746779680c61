#!/bin/bash

# 宠物电商前端项目备份脚本
# 作者: 系统管理员
# 创建时间: 2025-08-20

set -e  # 遇到错误时退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="pet-ecommerce-store-V20-frontend"
PROJECT_DIR="$(pwd)"
BACKUP_DIR="/home/<USER>/backups"
TIMESTAMP=$(date +%Y%m%d-%H%M%S)
BACKUP_FILE="${PROJECT_NAME}-backup-${TIMESTAMP}.tar.gz"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "宠物电商前端项目备份脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示此帮助信息"
    echo "  -v, --verify   验证备份文件完整性"
    echo "  -l, --list     列出所有备份文件"
    echo "  -c, --clean    清理30天前的备份文件"
    echo "  -f, --force    强制覆盖已存在的备份文件"
    echo ""
    echo "示例:"
    echo "  $0             创建新备份"
    echo "  $0 -v          验证最新备份"
    echo "  $0 -l          列出所有备份"
    echo "  $0 -c          清理旧备份"
}

# 检查项目目录
check_project() {
    if [[ ! -f "package.json" ]] || [[ ! -f "nuxt.config.ts" ]]; then
        log_error "当前目录不是有效的项目目录！"
        log_error "请确保在项目根目录下运行此脚本。"
        exit 1
    fi
    
    log_info "项目验证通过: $PROJECT_NAME"
}

# 创建备份目录
create_backup_dir() {
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_info "创建备份目录: $BACKUP_DIR"
        mkdir -p "$BACKUP_DIR"
    fi
}

# 创建备份
create_backup() {
    log_info "开始创建备份..."
    log_info "项目目录: $PROJECT_DIR"
    log_info "备份文件: $BACKUP_FILE"
    
    # 检查备份文件是否已存在
    if [[ -f "$BACKUP_DIR/$BACKUP_FILE" ]] && [[ "$1" != "--force" ]]; then
        log_warning "备份文件已存在: $BACKUP_FILE"
        read -p "是否覆盖? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "备份已取消"
            exit 0
        fi
    fi
    
    # 创建备份
    log_info "正在打包项目文件..."
    tar -czf "$BACKUP_DIR/$BACKUP_FILE" \
        --exclude=node_modules \
        --exclude=.nuxt \
        --exclude=.output \
        --exclude=.cache \
        --exclude=*.log \
        --exclude=.DS_Store \
        --exclude=.idea \
        --exclude=.vscode \
        --exclude=dist \
        --exclude=coverage \
        --exclude=.nyc_output \
        -C "$PROJECT_DIR" .
    
    if [[ $? -eq 0 ]]; then
        log_success "备份创建成功: $BACKUP_FILE"
        
        # 显示备份文件信息
        BACKUP_SIZE=$(du -h "$BACKUP_DIR/$BACKUP_FILE" | cut -f1)
        log_info "备份文件大小: $BACKUP_SIZE"
        log_info "备份文件位置: $BACKUP_DIR/$BACKUP_FILE"
        
        # 验证备份
        verify_backup "$BACKUP_FILE"
    else
        log_error "备份创建失败！"
        exit 1
    fi
}

# 验证备份
verify_backup() {
    local backup_file="$1"
    local backup_path="$BACKUP_DIR/$backup_file"
    
    if [[ -z "$backup_file" ]]; then
        # 如果没有指定文件，使用最新的备份
        backup_file=$(ls -t "$BACKUP_DIR"/${PROJECT_NAME}-backup-*.tar.gz 2>/dev/null | head -1 | xargs basename 2>/dev/null)
        if [[ -z "$backup_file" ]]; then
            log_error "未找到备份文件！"
            return 1
        fi
        backup_path="$BACKUP_DIR/$backup_file"
    fi
    
    if [[ ! -f "$backup_path" ]]; then
        log_error "备份文件不存在: $backup_path"
        return 1
    fi
    
    log_info "验证备份文件: $backup_file"
    
    # 创建临时目录进行验证
    local temp_dir=$(mktemp -d)
    
    # 解压到临时目录
    log_info "解压备份文件进行验证..."
    tar -xzf "$backup_path" -C "$temp_dir"
    
    # 检查关键文件
    local project_temp_dir="$temp_dir"
    local missing_files=()
    
    # 检查必需文件
    local required_files=(
        "package.json"
        "nuxt.config.ts"
        "tsconfig.json"
        "README.md"
        ".env"
        ".gitignore"
    )
    
    for file in "${required_files[@]}"; do
        if [[ ! -f "$project_temp_dir/$file" ]]; then
            missing_files+=("$file")
        fi
    done
    
    # 检查必需目录
    local required_dirs=(
        "woonuxt_base"
    )
    
    for dir in "${required_dirs[@]}"; do
        if [[ ! -d "$project_temp_dir/$dir" ]]; then
            missing_files+=("$dir/")
        fi
    done
    
    # 清理临时目录
    rm -rf "$temp_dir"
    
    if [[ ${#missing_files[@]} -eq 0 ]]; then
        log_success "备份验证通过！所有必需文件都存在。"
        
        # 显示备份统计信息
        local file_count=$(tar -tzf "$backup_path" | wc -l)
        local backup_size=$(du -h "$backup_path" | cut -f1)
        log_info "备份文件统计:"
        log_info "  - 文件总数: $file_count"
        log_info "  - 备份大小: $backup_size"
        
        return 0
    else
        log_error "备份验证失败！缺少以下文件:"
        for file in "${missing_files[@]}"; do
            log_error "  - $file"
        done
        return 1
    fi
}

# 列出所有备份
list_backups() {
    log_info "备份文件列表:"
    echo ""
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return
    fi
    
    local backup_files=($(ls -t "$BACKUP_DIR"/${PROJECT_NAME}-backup-*.tar.gz 2>/dev/null))
    
    if [[ ${#backup_files[@]} -eq 0 ]]; then
        log_warning "未找到备份文件"
        return
    fi
    
    printf "%-50s %-15s %-10s\n" "文件名" "大小" "创建时间"
    printf "%-50s %-15s %-10s\n" "----------------------------------------" "---------------" "----------"
    
    for file in "${backup_files[@]}"; do
        local filename=$(basename "$file")
        local size=$(du -h "$file" | cut -f1)
        local date=$(stat -c %y "$file" | cut -d' ' -f1)
        printf "%-50s %-15s %-10s\n" "$filename" "$size" "$date"
    done
    
    echo ""
    log_info "总计: ${#backup_files[@]} 个备份文件"
}

# 清理旧备份
clean_old_backups() {
    log_info "清理30天前的备份文件..."
    
    if [[ ! -d "$BACKUP_DIR" ]]; then
        log_warning "备份目录不存在: $BACKUP_DIR"
        return
    fi
    
    local old_backups=($(find "$BACKUP_DIR" -name "${PROJECT_NAME}-backup-*.tar.gz" -mtime +30 2>/dev/null))
    
    if [[ ${#old_backups[@]} -eq 0 ]]; then
        log_info "没有找到30天前的备份文件"
        return
    fi
    
    log_warning "将删除以下 ${#old_backups[@]} 个旧备份文件:"
    for file in "${old_backups[@]}"; do
        echo "  - $(basename "$file")"
    done
    
    read -p "确认删除? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        for file in "${old_backups[@]}"; do
            rm -f "$file"
            log_info "已删除: $(basename "$file")"
        done
        log_success "清理完成！"
    else
        log_info "清理已取消"
    fi
}

# 主函数
main() {
    # 检查参数
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--verify)
            verify_backup
            exit $?
            ;;
        -l|--list)
            list_backups
            exit 0
            ;;
        -c|--clean)
            clean_old_backups
            exit 0
            ;;
        -f|--force)
            check_project
            create_backup_dir
            create_backup --force
            exit 0
            ;;
        "")
            check_project
            create_backup_dir
            create_backup
            exit 0
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@" 