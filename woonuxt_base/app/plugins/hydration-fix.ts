export default defineNuxtPlugin((nuxtApp) => {
  // 修复水合不匹配问题
  if (process.client) {
    // 等待 DOM 完全加载后再执行客户端逻辑
    nuxtApp.hook('app:mounted', () => {
      // 延迟执行可能引起水合问题的操作
      setTimeout(() => {
        // 这里可以添加需要延迟执行的逻辑
      }, 100);
    });
  }

  // 处理路由变化时的水合问题
  nuxtApp.hook('page:finish', () => {
    if (process.client) {
      // 页面切换完成后重新检查水合状态
      nextTick(() => {
        // 可以在这里添加页面切换后的逻辑
      });
    }
  });
});
