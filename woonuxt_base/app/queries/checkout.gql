mutation Checkout(
  $billing: CustomerAddressInput = {}
  $metaData: [MetaDataInput] = { key: "", value: "" }
  $paymentMethod: String = "stripe"
  $shipping: CustomerAddressInput = {}
  $customerNote: String = ""
  $shipToDifferentAddress: Boolean = false
  $account: CreateAccountInput = { username: "", password: "" }
  $transactionId: String = ""
  $isPaid: Boolean = false
) {
  checkout(
    input: {
      paymentMethod: $paymentMethod
      billing: $billing
      metaData: $metaData
      shipping: $shipping
      customerNote: $customerNote
      shipToDifferentAddress: $shipToDifferentAddress
      account: $account
      transactionId: $transactionId
      isPaid: $isPaid
    }
  ) {
    result
    redirect
    order {
      needsPayment
      needsProcessing
      status
      databaseId
      orderKey
      subtotal
      total
      subtotal
      totalTax
      shippingTotal
      paymentMethodTitle
      paymentMethod
      date
      subtotal
      customer {
        email
      }
      lineItems {
        nodes {
          ...LineItem
        }
      }
    }
  }
}
