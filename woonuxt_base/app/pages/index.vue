<script lang="ts" setup>
import { ProductsOrderByEnum } from '#woo';
const { siteName, description, shortDescription, siteImage } = useAppConfig();

const { data } = await useAsyncGql('getProductCategories', { first: 6 });
const productCategories = data.value?.productCategories?.nodes || [];

const { data: productData } = await useAsyncGql('getProducts', { first: 5, orderby: ProductsOrderByEnum.POPULARITY });
const popularProducts = productData.value.products?.nodes || [];

useSeoMeta({
  title: `Home`,
  ogTitle: siteName,
  description: description,
  ogDescription: shortDescription,
  ogImage: siteImage,
  twitterCard: `summary_large_image`,
});
</script>

<template>
  <main>
    <HeroBanner />

    <!-- Pet Categories Section -->
    <section class="py-24 bg-gradient-to-br from-neutral-50 to-white">
      <div class="container">
        <div class="text-center mb-20">
          <div class="inline-flex items-center gap-2 bg-primary-100 text-primary-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <span>🐾</span>
            <span>Premium Pet Categories</span>
          </div>
          <h2 class="text-5xl font-bold text-neutral-900 mb-6 leading-tight">
            Shop for Your
            <span class="text-primary-600">Furry Friends</span>
          </h2>
          <p class="text-xl text-neutral-600 max-w-3xl mx-auto leading-relaxed">
            Discover premium supplies tailored specifically for dogs and cats. From nutritious food to cozy beds,
            we have everything your beloved companions need to live their happiest, healthiest lives.
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 max-w-5xl mx-auto">
          <!-- Dogs -->
          <NuxtLink to="/product-category/dogs" class="group">
            <div class="relative bg-gradient-to-br from-primary-50 via-primary-100 to-primary-200 rounded-3xl p-12 text-center hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-primary-200 overflow-hidden">
              <!-- Background decoration -->
              <div class="absolute inset-0 bg-gradient-to-br from-primary-400/10 to-primary-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

              <div class="relative z-10">
                <div class="text-8xl mb-8 group-hover:scale-110 transition-transform duration-500">🐕</div>
                <h3 class="text-3xl font-bold text-neutral-900 mb-4">Dogs</h3>
                <p class="text-lg text-neutral-600 mb-8 leading-relaxed">
                  Premium food, interactive toys, comfortable beds, training supplies, and health care essentials
                </p>

                <!-- Feature highlights -->
                <div class="flex justify-center gap-6 text-2xl mb-8 opacity-70 group-hover:opacity-100 transition-opacity duration-500">
                  <span title="Food & Treats">🦴</span>
                  <span title="Toys & Play">🎾</span>
                  <span title="Beds & Comfort">🏠</span>
                  <span title="Health & Care">💊</span>
                </div>

                <div class="inline-flex items-center bg-primary-600 text-white px-8 py-4 rounded-full font-semibold text-lg group-hover:bg-primary-700 transition-all duration-300 group-hover:scale-105">
                  Shop Dog Supplies
                  <svg class="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                  </svg>
                </div>
              </div>
            </div>
          </NuxtLink>

          <!-- Cats -->
          <NuxtLink to="/product-category/cats" class="group">
            <div class="relative bg-gradient-to-br from-purple-50 via-purple-100 to-purple-200 rounded-3xl p-12 text-center hover:shadow-2xl transition-all duration-700 transform hover:-translate-y-3 border border-purple-200 overflow-hidden">
              <!-- Background decoration -->
              <div class="absolute inset-0 bg-gradient-to-br from-purple-400/10 to-purple-600/20 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>

              <div class="relative z-10">
                <div class="text-8xl mb-8 group-hover:scale-110 transition-transform duration-500">🐱</div>
                <h3 class="text-3xl font-bold text-neutral-900 mb-4">Cats</h3>
                <p class="text-lg text-neutral-600 mb-8 leading-relaxed">
                  Quality litter, scratching posts, gourmet treats, cozy hideaways, and wellness products
                </p>

                <!-- Feature highlights -->
                <div class="flex justify-center gap-6 text-2xl mb-8 opacity-70 group-hover:opacity-100 transition-opacity duration-500">
                  <span title="Food & Treats">🐟</span>
                  <span title="Toys & Play">🧶</span>
                  <span title="Litter & Care">🏺</span>
                  <span title="Scratchers">🪵</span>
                </div>

                <div class="inline-flex items-center bg-purple-600 text-white px-8 py-4 rounded-full font-semibold text-lg group-hover:bg-purple-700 transition-all duration-300 group-hover:scale-105">
                  Shop Cat Supplies
                  <svg class="ml-3 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
                  </svg>
                </div>
              </div>
            </div>
          </NuxtLink>
        </div>

        <!-- Additional info section -->
        <div class="mt-20 text-center">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            <div class="flex flex-col items-center p-6">
              <div class="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mb-4">
                <span class="text-2xl">🏆</span>
              </div>
              <h4 class="font-semibold text-lg mb-2">Premium Quality</h4>
              <p class="text-neutral-600 text-sm">Only the finest products for your pets</p>
            </div>
            <div class="flex flex-col items-center p-6">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                <span class="text-2xl">🚚</span>
              </div>
              <h4 class="font-semibold text-lg mb-2">Fast Delivery</h4>
              <p class="text-neutral-600 text-sm">Quick shipping to keep your pets happy</p>
            </div>
            <div class="flex flex-col items-center p-6">
              <div class="w-16 h-16 bg-success-100 rounded-full flex items-center justify-center mb-4">
                <span class="text-2xl">💝</span>
              </div>
              <h4 class="font-semibold text-lg mb-2">Expert Care</h4>
              <p class="text-neutral-600 text-sm">Curated by pet care professionals</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Trust Indicators -->
    <section class="py-20 bg-neutral-50">
      <div class="container">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
          <div class="text-center group">
            <div class="w-20 h-20 bg-primary-500 rounded-2xl flex items-center justify-center text-white text-3xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"/>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-neutral-900 mb-4">Free Shipping</h3>
            <p class="text-neutral-600 leading-relaxed">On orders over $50. Fast and reliable delivery to your door with tracking included.</p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-success-500 rounded-2xl flex items-center justify-center text-white text-3xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-neutral-900 mb-4">Premium Quality</h3>
            <p class="text-neutral-600 leading-relaxed">Only the best products for your furry family members. Carefully curated and tested.</p>
          </div>

          <div class="text-center group">
            <div class="w-20 h-20 bg-accent-500 rounded-2xl flex items-center justify-center text-white text-3xl mx-auto mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg">
              <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
              </svg>
            </div>
            <h3 class="text-2xl font-bold text-neutral-900 mb-4">Happy Guarantee</h3>
            <p class="text-neutral-600 leading-relaxed">30-day return policy. Your pet's happiness is our priority, guaranteed or your money back.</p>
          </div>
        </div>
      </div>
    </section>

    <section class="container my-16">
      <div class="flex items-end justify-between">
        <h2 class="text-lg font-semibold md:text-2xl">{{ $t('messages.shop.shopByCategory') }}</h2>
        <NuxtLink class="text-primary" to="/categories">{{ $t('messages.general.viewAll') }}</NuxtLink>
      </div>
      <div class="grid justify-center grid-cols-2 gap-4 mt-8 md:grid-cols-3 lg:grid-cols-6">
        <CategoryCard v-for="(category, i) in productCategories" :key="i" class="w-full" :node="category" />
      </div>
    </section>

    <section class="container grid gap-4 my-24 md:grid-cols-2 lg:grid-cols-4">
      <div class="flex items-center gap-8 p-8 bg-white rounded-lg">
        <img src="/icons/box.svg" width="60" height="60" alt="Free Shipping" loading="lazy" />
        <div>
          <h3 class="text-xl font-semibold">Free Shipping</h3>
          <p class="text-sm">Free shipping on order over €50</p>
        </div>
      </div>
      <div class="flex items-center gap-8 p-8 bg-white rounded-lg">
        <img src="/icons/moneyback.svg" width="60" height="60" alt="Money Back" loading="lazy" />
        <div>
          <h3 class="text-xl font-semibold">Peace of Mind</h3>
          <p class="text-sm">30 days money back guarantee</p>
        </div>
      </div>
      <div class="flex items-center gap-8 p-8 bg-white rounded-lg">
        <img src="/icons/secure.svg" width="60" height="60" alt="Secure Payment" loading="lazy" />
        <div>
          <h3 class="text-xl font-semibold">100% Payment Secure</h3>
          <p class="text-sm">Your payment are safe with us.</p>
        </div>
      </div>
      <div class="flex items-center gap-8 p-8 bg-white rounded-lg">
        <img src="/icons/support.svg" width="60" height="60" alt="Support 24/7" loading="lazy" />
        <div>
          <h3 class="text-xl font-semibold">Support 24/7</h3>
          <p class="text-sm">24/7 Online support</p>
        </div>
      </div>
    </section>

    <section class="container my-16" v-if="popularProducts">
      <div class="flex items-end justify-between">
        <h2 class="text-lg font-semibold md:text-2xl">{{ $t('messages.shop.popularProducts') }}</h2>
        <NuxtLink class="text-primary" to="/products">{{ $t('messages.general.viewAll') }}</NuxtLink>
      </div>
      <ProductRow :products="popularProducts" class="grid-cols-2 md:grid-cols-4 lg:grid-cols-5 mt-8" />
    </section>
  </main>
</template>

<style scoped>
.brand img {
  max-height: min(8vw, 120px);
  object-fit: contain;
  object-position: center;
}
</style>
