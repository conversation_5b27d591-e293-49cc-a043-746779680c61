<script setup lang="ts">
const { isShowingSearch } = useSearching();
</script>

<template>
  <header class="sticky top-0 z-40 bg-white/95 backdrop-blur-md border-b border-neutral-200">
    <!-- Clean announcement bar -->
    <div class="bg-primary-600 text-white text-center py-2.5 text-sm font-medium">
      <div class="container">
        Free shipping on orders over $50 • 30-day returns • Premium pet supplies
      </div>
    </div>

    <!-- Main header -->
    <div class="container">
      <div class="flex items-center justify-between py-5">
        <!-- Logo section -->
        <div class="flex items-center">
          <MenuTrigger class="lg:hidden mr-3" />
          <Logo />
        </div>

        <!-- Navigation menu -->
        <MainMenu class="items-center hidden gap-8 text-sm font-medium text-neutral-800 lg:flex" />

        <!-- Right section -->
        <div class="flex items-center gap-3">
          <!-- Search -->
          <div class="hidden md:block">
            <ProductSearch class="w-80" />
          </div>
          <SearchTrigger class="md:hidden p-2 hover:bg-neutral-100 rounded-lg transition-colors" />

          <!-- User actions -->
          <div class="flex items-center gap-2">
            <SignInLink class="p-2 hover:bg-neutral-100 rounded-lg transition-colors text-neutral-700 hover:text-primary-600" />
            <CartTrigger class="p-2 hover:bg-neutral-100 rounded-lg transition-colors text-neutral-700 hover:text-primary-600" />
          </div>
        </div>
      </div>

      <!-- Mobile search -->
      <Transition name="scale-y" mode="out-in">
        <div class="pb-4 md:hidden" v-if="isShowingSearch">
          <ProductSearch class="w-full" />
        </div>
      </Transition>
    </div>
  </header>
</template>
