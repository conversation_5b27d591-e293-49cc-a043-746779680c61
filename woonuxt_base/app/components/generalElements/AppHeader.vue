<script setup lang="ts">
const { isShowingSearch } = useSearching();
</script>

<template>
  <header class="sticky top-0 z-40 bg-white/95 backdrop-blur-md border-b border-neutral-200 shadow-sm">
    <!-- Enhanced announcement bar -->
    <div class="bg-gradient-to-r from-primary-600 via-primary-500 to-purple-600 text-white text-center py-3 text-sm font-medium relative overflow-hidden">
      <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
      <div class="container relative z-10 flex items-center justify-center gap-2">
        <span class="animate-bounce">🐾</span>
        <span>Free shipping on orders over $50 • 30-day returns • Premium dog & cat supplies</span>
        <span class="animate-bounce" style="animation-delay: 0.5s;">🐾</span>
      </div>
    </div>

    <!-- Main header -->
    <div class="container">
      <div class="flex items-center justify-between py-5">
        <!-- Logo section -->
        <div class="flex items-center">
          <MenuTrigger class="lg:hidden mr-3" />
          <Logo />
        </div>

        <!-- Navigation menu -->
        <MainMenu class="items-center hidden gap-8 text-sm font-medium text-neutral-800 lg:flex" />

        <!-- Right section -->
        <div class="flex items-center gap-3">
          <!-- Search -->
          <div class="hidden md:block">
            <ProductSearch class="w-80" />
          </div>
          <SearchTrigger class="md:hidden p-2 hover:bg-neutral-100 rounded-lg transition-colors" />

          <!-- User actions -->
          <div class="flex items-center gap-2">
            <SignInLink class="p-2 hover:bg-neutral-100 rounded-lg transition-colors text-neutral-700 hover:text-primary-600" />
            <CartTrigger class="p-2 hover:bg-neutral-100 rounded-lg transition-colors text-neutral-700 hover:text-primary-600" />
          </div>
        </div>
      </div>

      <!-- Mobile search -->
      <Transition name="scale-y" mode="out-in">
        <div class="pb-4 md:hidden" v-if="isShowingSearch">
          <ProductSearch class="w-full" />
        </div>
      </Transition>
    </div>
  </header>
</template>
