<template>
  <section class="relative bg-gradient-to-br from-neutral-50 to-white overflow-hidden">
    <!-- Background pattern -->
    <div class="absolute inset-0 opacity-5">
      <div class="absolute inset-0" style="background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0); background-size: 20px 20px;"></div>
    </div>

    <!-- Main content -->
    <div class="relative container mx-auto px-4 py-16 lg:py-24">
      <div class="grid lg:grid-cols-2 gap-12 items-center">
        <!-- Text content -->
        <div class="space-y-8">
          <div class="space-y-4">
            <div class="inline-flex items-center gap-2 px-4 py-2 bg-primary-50 text-primary-700 rounded-full text-sm font-medium">
              <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
              </svg>
              Premium Pet Supplies
            </div>

            <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-neutral-900 leading-tight">
              Everything Your
              <span class="text-primary-600">Dogs & Cats</span>
              Need
            </h1>

            <p class="text-xl text-neutral-600 leading-relaxed max-w-lg">
              Discover premium supplies for dogs and cats, from nutritious food to cozy beds and fun toys.
              Give your furry companions the care they deserve.
            </p>
          </div>

          <!-- CTA Buttons -->
          <div class="flex flex-col sm:flex-row gap-4">
            <NuxtLink
              to="/products"
              class="inline-flex items-center justify-center px-8 py-4 bg-primary-600 text-white font-semibold rounded-xl hover:bg-primary-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              Shop Now
              <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"/>
              </svg>
            </NuxtLink>

            <NuxtLink
              to="/categories"
              class="inline-flex items-center justify-center px-8 py-4 bg-white text-neutral-700 font-semibold rounded-xl border-2 border-neutral-200 hover:border-primary-400 hover:text-primary-700 transition-all duration-200">
              Browse Categories
            </NuxtLink>
          </div>

          <!-- Trust indicators -->
          <div class="flex items-center gap-8 pt-4">
            <div class="flex items-center gap-2 text-sm text-neutral-600">
              <div class="w-5 h-5 bg-success-500 rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <span>Free Shipping</span>
            </div>

            <div class="flex items-center gap-2 text-sm text-neutral-600">
              <div class="w-5 h-5 bg-success-500 rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <span>30-Day Returns</span>
            </div>

            <div class="flex items-center gap-2 text-sm text-neutral-600">
              <div class="w-5 h-5 bg-success-500 rounded-full flex items-center justify-center">
                <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                </svg>
              </div>
              <span>Premium Quality</span>
            </div>
          </div>
        </div>

        <!-- Hero image -->
        <div class="relative">
          <div class="relative bg-gradient-to-br from-primary-50 to-primary-100 rounded-3xl p-8 lg:p-12 overflow-hidden">
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-primary-200/20 to-purple-200/20"></div>

            <!-- Pet illustrations -->
            <div class="relative text-center space-y-8">
              <!-- Main pets display -->
              <div class="flex justify-center items-center gap-8">
                <div class="transform hover:scale-110 transition-transform duration-300">
                  <div class="text-7xl lg:text-8xl">🐕</div>
                </div>
                <div class="text-4xl lg:text-5xl opacity-60">❤️</div>
                <div class="transform hover:scale-110 transition-transform duration-300">
                  <div class="text-7xl lg:text-8xl">🐱</div>
                </div>
              </div>

              <div class="space-y-2">
                <h3 class="text-2xl lg:text-3xl font-bold text-neutral-800">Happy Dogs & Cats</h3>
                <p class="text-neutral-600">Premium supplies for your furry family</p>
              </div>

              <!-- Product icons -->
              <div class="grid grid-cols-4 gap-4 max-w-xs mx-auto">
                <div class="flex flex-col items-center space-y-1 opacity-70 hover:opacity-100 transition-opacity">
                  <span class="text-2xl">🦴</span>
                  <span class="text-xs text-neutral-500">Food</span>
                </div>
                <div class="flex flex-col items-center space-y-1 opacity-70 hover:opacity-100 transition-opacity">
                  <span class="text-2xl">🎾</span>
                  <span class="text-xs text-neutral-500">Toys</span>
                </div>
                <div class="flex flex-col items-center space-y-1 opacity-70 hover:opacity-100 transition-opacity">
                  <span class="text-2xl">🏠</span>
                  <span class="text-xs text-neutral-500">Beds</span>
                </div>
                <div class="flex flex-col items-center space-y-1 opacity-70 hover:opacity-100 transition-opacity">
                  <span class="text-2xl">💊</span>
                  <span class="text-xs text-neutral-500">Health</span>
                </div>
              </div>
            </div>

            <!-- Floating badges -->
            <div class="absolute -top-4 -right-4 bg-primary-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
              New Arrivals
            </div>
            <div class="absolute -bottom-4 -left-4 bg-purple-600 text-white px-4 py-2 rounded-full text-sm font-semibold shadow-lg">
              Best Sellers
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
