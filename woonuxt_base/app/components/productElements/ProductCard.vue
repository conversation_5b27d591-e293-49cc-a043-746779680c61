<script setup lang="ts">
const route = useRoute();
const { storeSettings } = useAppConfig();
const props = defineProps({
  node: { type: Object as PropType<Product>, required: true },
  index: { type: Number, default: 1 },
});

const imgWidth = 280;
const imgHeight = Math.round(imgWidth * 1.125);

// example: ?filter=pa_color[green,blue],pa_size[large]
const filterQuery = ref(route.query?.filter as string);
const paColor = ref(filterQuery.value?.split('pa_color[')[1]?.split(']')[0]?.split(',') || []);

// watch filterQuery
watch(
  () => route.query,
  () => {
    filterQuery.value = route.query.filter as string;
    paColor.value = filterQuery.value?.split('pa_color[')[1]?.split(']')[0]?.split(',') || [];
  },
);

const mainImage = computed<string>(() => props.node?.image?.producCardSourceUrl || props.node?.image?.sourceUrl || '/images/placeholder.jpg');
const imagetoDisplay = computed<string>(() => {
  if (paColor.value.length) {
    const activeColorImage = props.node?.variations?.nodes.filter((variation) => {
      const hasMatchingAttributes = variation.attributes?.nodes.some((attribute) => paColor.value.some((color) => attribute?.value?.includes(color)));
      const hasMatchingSlug = paColor.value.some((color) => variation.slug?.includes(color));
      return hasMatchingAttributes || hasMatchingSlug;
    });
    if (activeColorImage?.length) return activeColorImage[0]?.image?.producCardSourceUrl || activeColorImage[0]?.image?.sourceUrl || mainImage.value;
  }
  return mainImage.value;
});
</script>

<template>
  <div class="group bg-white rounded-2xl border border-neutral-200 hover:border-neutral-300 transition-all duration-300 hover:shadow-lg overflow-hidden">
    <!-- Product image container -->
    <div class="relative aspect-square bg-neutral-50 overflow-hidden">
      <NuxtLink v-if="node.slug" :to="`/product/${decodeURIComponent(node.slug)}`" :title="node.name">
        <!-- Sale badge -->
        <SaleBadge :node class="absolute top-3 right-3 z-10" />

        <!-- Wishlist button -->
        <div class="absolute top-3 left-3 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <WishlistButton
            :product="node"
            class="bg-white/95 backdrop-blur-sm p-2 rounded-full shadow-sm hover:shadow-md transition-all duration-200"
          />
        </div>

        <!-- Product image -->
        <NuxtImg
          v-if="imagetoDisplay"
          :width="imgWidth"
          :height="imgHeight"
          :src="imagetoDisplay"
          :alt="node.image?.altText || node.name || 'Product image'"
          :title="node.image?.title || node.name"
          :loading="index <= 3 ? 'eager' : 'lazy'"
          :sizes="`sm:${imgWidth / 2}px md:${imgWidth}px`"
          class="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
          placeholder
          placeholder-class="blur-xl" />

        <!-- Quick actions overlay -->
        <div class="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors duration-300">
          <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 translate-y-full group-hover:translate-y-0 transition-transform duration-300">
            <button class="bg-white text-neutral-900 px-6 py-2 rounded-full text-sm font-medium shadow-lg hover:shadow-xl transition-all duration-200 border border-neutral-200">
              Quick View
            </button>
          </div>
        </div>
      </NuxtLink>
    </div>

    <!-- Product info -->
    <div class="p-5 space-y-3">
      <!-- Rating and category -->
      <div class="flex items-center justify-between">
        <StarRating
          v-if="storeSettings.showReviews"
          :rating="node.averageRating"
          :count="node.reviewCount"
          class="text-accent-500"
        />
        <span class="text-xs text-neutral-500 bg-neutral-100 px-2 py-1 rounded-full">
          Premium
        </span>
      </div>

      <!-- Product name -->
      <NuxtLink v-if="node.slug" :to="`/product/${decodeURIComponent(node.slug)}`" :title="node.name">
        <h3 class="font-semibold text-neutral-900 leading-snug group-hover:text-primary-600 transition-colors duration-200 line-clamp-2">
          {{ node.name }}
        </h3>
      </NuxtLink>

      <!-- Price -->
      <div class="flex items-center justify-between">
        <ProductPrice
          class="text-lg font-bold text-neutral-900"
          :sale-price="node.salePrice"
          :regular-price="node.regularPrice"
        />
      </div>

      <!-- Add to cart button -->
      <div class="pt-2">
        <AddToCartButton
          :product="node"
          class="w-full bg-primary-500 hover:bg-primary-600 text-white py-3 px-4 rounded-xl font-medium transition-colors duration-200 flex items-center justify-center gap-2"
        >
          <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9"/>
          </svg>
          Add to Cart
        </AddToCartButton>
      </div>
    </div>
  </div>
</template>
