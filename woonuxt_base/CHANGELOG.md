# Change Log

All notable changes to this project will be documented in this file. I'll try my best to keep it updated.

## v4.0.16 (22-07-2024)`

- feature: Add option showMoveToWishlist #192
- feature: Add browse product button to empty cart #194
- feature: Add autoComplete to multiple forms #200
- refactor: Reduce the number of API call on checkout page
- fix: Order terms order #191
- fix: link in lost-password #195
- fix: Initial country value for states #197
- chore: Add @nuxt/icon module for icon support
- chore: Update ShippingOptions component with currency symbol from runtime config
- chore: Update npm dependencies to latest versions
- chore: Update `.env.example` #190
- chore: Refactor QuantityInput component
- chore: Other minor improvements and bug fixes

## v4.0.6 (4-07-2024)`

- Add user avatar and email to sidebar of account page
- Add Wishlist to my account page
- Add ResetPassword from email #180
- Add new Nuxt `compatibilityDate`

## v4.0.5 (4-07-2024)

- Handle server errors in useGqlError callback
- Improve order summary page layout and styling
- Add null check for date in formatDate function
- Better TypeScript types in index.d.ts
- Add error logging for GraphQL queries in useAuth.ts
- Updated queries & fragment

## v4.0.4 (4-07-2024)

- Fix downloadable products not showing on the correct order summary page

## v4.0.3 (1-07-2024)

- Add Downloads List #177
- Add option to show/hide Breadcrumb on SingleProduct #181

## v4.0.2 (29-06-2024)

- Add more options to `app.config.ts` #178

## v4.0.1 (26-06-2024)

- Add option showReviews #176
- Update `nuxt-graphql-client` to v0.2.35

## v4.0.0 (19-06-2024)

- Enable Nuxt 4 compatibility option in `nuxt.config.js`
- Updated folder structure to new `app` directory. See [New Directory Structure](https://nuxt.com/docs/getting-started/upgrade#new-directory-structure)
- Update dependencies
- Add `app.config.ts` with some default values. Note, this will be expanded in future versions to include more configuration options.
- App Popular products section to the home page
- Minor TS improvements
- Add `#constants` and `#woo` aliases to `nuxt.config.js`. The `#woo` aliase is useful for importing WooCommerce GraphQL types and enums generated by `nuxt-graphql-client` codegen.
- WooNuxt v3 will still be available in the [v3 branch](https://github.com/scottyzen/woonuxt/tree/v3)

# 3.6.2 (20-05-2024)

Add `AUTO_OPEN_CART` to `.env` to automatically open the cart when adding a product to the cart.

# 3.6.1 (17-05-2024)

## CHANGES

- Handle login error and improve error message in `useAuth.ts`
- Remove `formatURI` function
- Minor TypeScript improvements
- Update checkout page to use TypeScript in script setup block
- Add `logGQLError` & `clearAllLocalStorage` helper functions
- Update npm dependencies for `@nuxt/image` and `@stripe/stripe-js`

# 3.6.0 (27-04-2024)

## CHANGES

- Updated dependencies
- Fix images not loading on Netlify
- Add users avatar to the header when logged in
- Add placeholder fot category and product images
- Add basic `error.vue` page

# 3.5.0

## CHANGES (19-04-2024)

- Update styling of PriceFilter component
- Switch slider to Primevue
- Update hover text color in Filters component
- Add keep-alive to NuxtPage (Improves DX)
- fetchAllProducts to get all products
- Updated dependencies

## 3.4.27 (06-04-2024)

### Bug Fixes

- The default variation is now respected. Also, if there are no defaults set, the first of each attribute is selected. IMO I think that is better than having the user select the first option for each attribute. [#141](https://github.com/scottyzen/woonuxt/issues/141)

## 3.4.26 (06-04-2024)

### Bug Fixes

- Fix bug where a product variation that wasn't available was able to be added to the cart.
- Product gallery showing not all variations and won't change image [#139](https://github.com/scottyzen/woonuxt/issues/139)
