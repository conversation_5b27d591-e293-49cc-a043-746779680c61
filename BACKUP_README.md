# 项目备份脚本使用说明

## 概述

`backup.sh` 是一个专门为宠物电商前端项目设计的完整备份脚本，提供了自动化的项目备份、验证和管理功能。

## 功能特性

- ✅ **智能备份**: 自动排除临时文件和构建产物
- ✅ **完整性验证**: 自动验证备份文件的完整性
- ✅ **备份管理**: 列出、清理旧备份文件
- ✅ **彩色输出**: 友好的彩色日志输出
- ✅ **错误处理**: 完善的错误处理和用户提示

## 使用方法

### 基本用法

```bash
# 创建新备份
./backup.sh

# 显示帮助信息
./backup.sh --help
```

### 高级功能

```bash
# 验证最新备份的完整性
./backup.sh --verify

# 列出所有备份文件
./backup.sh --list

# 清理30天前的旧备份
./backup.sh --clean

# 强制覆盖已存在的备份文件
./backup.sh --force
```

## 备份内容

### 包含的文件
- ✅ 所有源代码文件
- ✅ 配置文件 (`package.json`, `nuxt.config.ts`, `tsconfig.json`)
- ✅ 环境配置文件 (`.env`)
- ✅ 项目文档 (`README.md`, `CONTRIBUTING.md`, `LICENSE`)
- ✅ 部署配置 (`netlify.toml`)
- ✅ 版本控制配置 (`.gitignore`, `.nvmrc`)
- ✅ 核心主题文件 (`woonuxt_base/` 目录)

### 排除的文件
- ❌ `node_modules/` (可通过 `npm install` 重新生成)
- ❌ `.nuxt/` (构建时生成)
- ❌ `.output/` (构建时生成)
- ❌ `.cache/` (缓存文件)
- ❌ `*.log` (日志文件)
- ❌ `.DS_Store`, `.idea`, `.vscode` (IDE 文件)
- ❌ `dist/`, `coverage/` (构建产物)

## 备份文件信息

- **备份目录**: `/home/<USER>/backups/`
- **文件命名**: `pet-ecommerce-store-V20-frontend-backup-YYYYMMDD-HHMMSS.tar.gz`
- **文件格式**: 压缩的 tar.gz 格式
- **典型大小**: 约 1-2 MB

## 恢复项目

从备份恢复项目的步骤：

1. **解压备份文件**:
   ```bash
   tar -xzf /home/<USER>/backups/pet-ecommerce-store-V20-frontend-backup-YYYYMMDD-HHMMSS.tar.gz
   ```

2. **进入项目目录**:
   ```bash
   cd pet-ecommerce-store-V20-frontend
   ```

3. **安装依赖**:
   ```bash
   npm install
   ```

4. **启动开发服务器**:
   ```bash
   npm run dev
   ```

## 自动化建议

### 定时备份

可以设置 cron 任务进行定时备份：

```bash
# 编辑 crontab
crontab -e

# 添加定时任务（每天凌晨2点备份）
0 2 * * * cd /home/<USER>/pet-ecommerce-store-V20-frontend && ./backup.sh --force
```

### 备份到远程

建议将备份文件同步到远程存储：

```bash
# 使用 rsync 同步到远程服务器
rsync -av /home/<USER>/backups/ user@remote-server:/backups/pet-ecommerce/

# 或使用云存储服务
# 例如：AWS S3, Google Cloud Storage, 阿里云 OSS 等
```

## 故障排除

### 常见问题

1. **权限错误**
   ```bash
   chmod +x backup.sh
   ```

2. **备份目录不存在**
   - 脚本会自动创建备份目录

3. **磁盘空间不足**
   - 使用 `./backup.sh --clean` 清理旧备份
   - 检查磁盘空间: `df -h`

4. **备份验证失败**
   - 检查项目文件是否完整
   - 重新运行备份脚本

### 日志查看

脚本会显示详细的彩色日志信息：
- 🔵 **蓝色**: 信息提示
- 🟢 **绿色**: 成功操作
- 🟡 **黄色**: 警告信息
- 🔴 **红色**: 错误信息

## 安全建议

1. **定期清理**: 建议每月清理一次30天前的备份
2. **多重备份**: 将备份文件存储到多个位置
3. **权限控制**: 确保备份文件有适当的访问权限
4. **加密备份**: 对于敏感项目，考虑对备份文件进行加密

## 联系支持

如果遇到问题，请检查：
1. 脚本是否在项目根目录运行
2. 是否有足够的磁盘空间
3. 是否有必要的文件权限

---

*最后更新: 2025-08-20* 