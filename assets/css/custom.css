/* Custom styles for PawMart - Dogs & Cats Store */

/* Smooth scrolling and improved typography */
html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(249, 115, 22, 0.3); }
  50% { box-shadow: 0 0 30px rgba(249, 115, 22, 0.5); }
}

@keyframes pulse-glow {
  0%, 100% { box-shadow: 0 0 10px rgba(168, 85, 247, 0.3); }
  50% { box-shadow: 0 0 20px rgba(168, 85, 247, 0.6); }
}

/* Utility classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 3s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Custom gradient backgrounds */
.bg-pet-gradient {
  background: linear-gradient(135deg, #f97316 0%, #a855f7 100%);
}

.bg-dog-gradient {
  background: linear-gradient(135deg, #f97316 0%, #fb923c 100%);
}

.bg-cat-gradient {
  background: linear-gradient(135deg, #a855f7 0%, #c084fc 100%);
}

/* Enhanced button styles */
.btn-primary {
  @apply bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105;
}

.btn-secondary {
  @apply bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105;
}

/* Custom scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced card styles */
.card-hover {
  @apply transition-all duration-500 hover:shadow-2xl hover:-translate-y-2;
}

.card-glow {
  @apply hover:shadow-2xl hover:shadow-primary-200/50;
}

/* Pet-themed decorations */
.paw-pattern {
  background-image: radial-gradient(circle at 20px 20px, rgba(249, 115, 22, 0.1) 2px, transparent 2px);
  background-size: 40px 40px;
}

/* Loading states */
.loading-shimmer {
  background: linear-gradient(90deg, #f1f5f9 25%, #e2e8f0 50%, #f1f5f9 75%);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* Enhanced focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
}

/* Text effects */
.text-gradient {
  background: linear-gradient(135deg, #f97316, #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced transitions */
.transition-smooth {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.transition-bounce {
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Mobile optimizations */
@media (max-width: 768px) {
  .mobile-padding {
    @apply px-4;
  }
  
  .mobile-text {
    @apply text-sm;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .card-hover {
    border: 2px solid currentColor;
  }
}

/* Dark mode preparations (for future use) */
@media (prefers-color-scheme: dark) {
  /* Dark mode styles can be added here in the future */
}
