<script setup lang="ts">
const { wooNuxtVersionInfo } = useHelpers();
const { wishlistLink } = useAuth();
</script>

<template>
  <footer class="bg-neutral-900 text-white order-last">
    <!-- Main footer content -->
    <div class="container py-16">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
        <!-- Brand section -->
        <div class="lg:col-span-2 space-y-6">
          <div class="flex items-center gap-3">
            <div class="w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
              </svg>
            </div>
            <div>
              <h3 class="text-2xl font-bold">PawMart</h3>
              <p class="text-neutral-400 text-sm">Pet Supplies & More</p>
            </div>
          </div>
          
          <WebsiteShortDescription class="text-gray-300 leading-relaxed" />
          
          <!-- Newsletter signup -->
          <div class="space-y-3">
            <h4 class="font-semibold text-lg">Stay Updated 📧</h4>
            <p class="text-gray-400 text-sm">Get the latest pet care tips and exclusive offers!</p>
            <div class="flex gap-2">
              <input 
                type="email" 
                placeholder="Enter your email" 
                class="flex-1 px-4 py-2 rounded-lg bg-gray-700 border border-gray-600 text-white placeholder-gray-400 focus:border-primary focus:outline-none"
              />
              <button class="px-6 py-2 bg-primary hover:bg-primary-dark rounded-lg font-medium transition-colors">
                Subscribe
              </button>
            </div>
          </div>
          
          <LangSwitcher class="mt-6" />
        </div>

        <!-- Pet Categories -->
        <div class="space-y-4">
          <h4 class="font-semibold text-lg flex items-center gap-2">
            🐾 Pet Categories
          </h4>
          <div class="space-y-3 text-sm">
            <NuxtLink to="/product-category/dogs" class="flex items-center gap-2 text-gray-300 hover:text-primary transition-colors group">
              <span class="text-lg group-hover:scale-110 transition-transform">🐕</span>
              <span>Dog Supplies</span>
            </NuxtLink>
            <NuxtLink to="/product-category/cats" class="flex items-center gap-2 text-gray-300 hover:text-primary transition-colors group">
              <span class="text-lg group-hover:scale-110 transition-transform">🐱</span>
              <span>Cat Supplies</span>
            </NuxtLink>
            <NuxtLink to="/products" class="flex items-center gap-2 text-gray-300 hover:text-primary transition-colors group">
              <span class="text-lg group-hover:scale-110 transition-transform">🛍️</span>
              <span>All Products</span>
            </NuxtLink>
          </div>
        </div>

        <!-- Products -->
        <div class="space-y-4">
          <h4 class="font-semibold text-lg flex items-center gap-2">
            🛍️ Products
          </h4>
          <div class="space-y-2 text-sm">
            <NuxtLink to="/products" class="block text-gray-300 hover:text-primary transition-colors">{{ $t('messages.shop.newArrivals') }}</NuxtLink>
            <NuxtLink to="/products?filter=sale[true]" class="block text-gray-300 hover:text-primary transition-colors">On Sale</NuxtLink>
            <NuxtLink to="/products?orderby=rating&order=ASC&filter=rating[1]" class="block text-gray-300 hover:text-primary transition-colors">Top Rated</NuxtLink>
            <NuxtLink to="/products?filter=featured[true]" class="block text-gray-300 hover:text-primary transition-colors">Featured</NuxtLink>
            <a href="/" class="block text-gray-300 hover:text-primary transition-colors">{{ $t('messages.shop.giftCards') }}</a>
          </div>
        </div>

        <!-- Customer Service -->
        <div class="space-y-4">
          <h4 class="font-semibold text-lg flex items-center gap-2">
            💬 Support
          </h4>
          <div class="space-y-2 text-sm">
            <NuxtLink to="/contact" class="block text-gray-300 hover:text-primary transition-colors">Contact Us</NuxtLink>
            <NuxtLink to="/my-account/" class="block text-gray-300 hover:text-primary transition-colors">{{ $t('messages.account.myAccount') }}</NuxtLink>
            <NuxtLink :to="wishlistLink" class="block text-gray-300 hover:text-primary transition-colors">{{ $t('messages.shop.wishlist') }}</NuxtLink>
            <a href="/" class="block text-gray-300 hover:text-primary transition-colors">Shipping & Returns</a>
            <a href="/" class="block text-gray-300 hover:text-primary transition-colors">Privacy Policy</a>
          </div>
        </div>
      </div>
    </div>

    <!-- Bottom bar -->
    <div class="border-t border-gray-700">
      <div class="container py-6">
        <div class="flex flex-col md:flex-row items-center justify-between gap-4">
          <div class="flex items-center gap-6">
            <p class="text-sm text-gray-400">
              © 2024 PawMart. Made with ❤️ for pet lovers.
            </p>
            <div class="flex items-center gap-4 text-xs text-gray-500">
              <span class="flex items-center gap-1">
                <span>🔒</span>
                <span>Secure Checkout</span>
              </span>
              <span class="flex items-center gap-1">
                <span>🚚</span>
                <span>Fast Shipping</span>
              </span>
              <span class="flex items-center gap-1">
                <span>💝</span>
                <span>30-Day Returns</span>
              </span>
            </div>
          </div>
          
          <div class="flex items-center gap-4">
            <SocialIcons />
            <div class="text-xs text-gray-500">
              <a href="https://woonuxt.com" :title="`WooNuxt v${wooNuxtVersionInfo}`" class="hover:text-primary transition-colors">
                Powered by WooNuxt v{{ wooNuxtVersionInfo }}
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<style scoped lang="postcss">
a {
  @apply transition-colors duration-200;
}

a:hover {
  @apply text-primary;
}
</style>
