<script setup lang="ts">
const { siteName } = useAppConfig();
</script>

<template>
  <NuxtLink to="/" class="flex items-center gap-3 group">
    <!-- Logo Icon -->
    <div class="relative">
      <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center group-hover:from-primary-600 group-hover:to-primary-700 transition-all duration-300 shadow-lg group-hover:shadow-glow-primary group-hover:scale-105">
        <!-- Pet paw icon -->
        <div class="flex items-center gap-0.5">
          <span class="text-lg animate-pulse">🐕</span>
          <span class="text-xs">❤️</span>
          <span class="text-lg animate-pulse" style="animation-delay: 0.5s;">🐱</span>
        </div>
      </div>

      <!-- Floating decoration -->
      <div class="absolute -top-1 -right-1 w-3 h-3 bg-purple-500 rounded-full animate-bounce opacity-80"></div>
    </div>

    <!-- Logo Text -->
    <div class="flex flex-col">
      <h1 class="text-xl font-bold text-neutral-900 group-hover:text-primary-600 transition-colors duration-200">
        {{ siteName }}
      </h1>
      <p class="text-xs text-neutral-500 -mt-0.5 font-medium">Dogs & Cats Supplies</p>
    </div>
  </NuxtLink>
</template>
