<script setup lang="ts">
const { siteName } = useAppConfig();
</script>

<template>
  <NuxtLink to="/" class="flex items-center gap-3 group">
    <!-- Logo Icon -->
    <div class="relative">
      <div class="w-10 h-10 bg-primary-500 rounded-xl flex items-center justify-center group-hover:bg-primary-600 transition-colors duration-200">
        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.94-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z"/>
        </svg>
      </div>
    </div>

    <!-- Logo Text -->
    <div class="flex flex-col">
      <h1 class="text-xl font-bold text-neutral-900 group-hover:text-primary-600 transition-colors duration-200">
        {{ siteName }}
      </h1>
      <p class="text-xs text-neutral-500 -mt-0.5">Pet Supplies & More</p>
    </div>
  </NuxtLink>
</template>
